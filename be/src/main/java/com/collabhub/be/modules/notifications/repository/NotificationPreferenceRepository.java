package com.collabhub.be.modules.notifications.repository;

import com.collabhub.be.modules.notifications.dto.NotificationChannel;
import com.collabhub.be.modules.notifications.dto.NotificationType;
import org.jooq.DSLContext;
import org.jooq.generated.tables.daos.NotificationPreferenceDao;
import org.jooq.generated.tables.pojos.NotificationPreference;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.jooq.generated.Tables.NOTIFICATION_PREFERENCE;

/**
 * Repository for NotificationPreference entity using jOOQ for database operations.
 * Provides multi-tenant aware queries scoped by user_id.
 */
@Repository
public class NotificationPreferenceRepository extends NotificationPreferenceDao {

    private final DSLContext dsl;

    public NotificationPreferenceRepository(DSLContext dsl) {
        super(dsl.configuration());
        this.dsl = dsl;
    }

    /**
     * Finds all notification preferences for a specific user.
     *
     * @param userId the user ID
     * @return list of notification preferences
     */
    public List<NotificationPreference> findByUserId(Long userId) {
        return dsl.select()
                .from(NOTIFICATION_PREFERENCE)
                .where(NOTIFICATION_PREFERENCE.USER_ID.eq(userId))
                .orderBy(NOTIFICATION_PREFERENCE.TYPE, NOTIFICATION_PREFERENCE.CHANNEL)
                .fetchInto(NotificationPreference.class);
    }

    /**
     * Finds all notification preferences for a specific email.
     * Used for external participants who have global preferences across accounts.
     *
     * @param email the email address
     * @return list of notification preferences
     */
    public List<NotificationPreference> findByEmail(String email) {
        return dsl.select()
                .from(NOTIFICATION_PREFERENCE)
                .where(NOTIFICATION_PREFERENCE.EMAIL.eq(email))
                .and(NOTIFICATION_PREFERENCE.USER_ID.isNull())
                .orderBy(NOTIFICATION_PREFERENCE.TYPE, NOTIFICATION_PREFERENCE.CHANNEL)
                .fetchInto(NotificationPreference.class);
    }

    /**
     * Finds a specific notification preference by user, type, and channel.
     *
     * @param userId the user ID
     * @param type the notification type
     * @param channel the notification channel
     * @return optional notification preference
     */
    public Optional<NotificationPreference> findByUserTypeAndChannel(Long userId, NotificationType type, NotificationChannel channel) {
        return dsl.select()
                .from(NOTIFICATION_PREFERENCE)
                .where(NOTIFICATION_PREFERENCE.USER_ID.eq(userId))
                .and(NOTIFICATION_PREFERENCE.TYPE.eq(type.toJooqEnum()))
                .and(NOTIFICATION_PREFERENCE.CHANNEL.eq(channel.toJooqEnum()))
                .fetchOptionalInto(NotificationPreference.class);
    }

    /**
     * Finds a specific notification preference by email, type, and channel.
     * Used for external participants with global preferences.
     *
     * @param email the email address
     * @param type the notification type
     * @param channel the notification channel
     * @return optional notification preference
     */
    public Optional<NotificationPreference> findByEmailTypeAndChannel(String email, NotificationType type, NotificationChannel channel) {
        return dsl.select()
                .from(NOTIFICATION_PREFERENCE)
                .where(NOTIFICATION_PREFERENCE.EMAIL.eq(email))
                .and(NOTIFICATION_PREFERENCE.USER_ID.isNull())
                .and(NOTIFICATION_PREFERENCE.TYPE.eq(type.toJooqEnum()))
                .and(NOTIFICATION_PREFERENCE.CHANNEL.eq(channel.toJooqEnum()))
                .fetchOptionalInto(NotificationPreference.class);
    }

    /**
     * Creates or updates a notification preference for an internal user.
     *
     * @param userId the user ID
     * @param email the user email
     * @param type the notification type
     * @param channel the notification channel
     * @param enabled whether the preference is enabled
     * @return the created or updated preference
     */
    public NotificationPreference upsertPreferenceForUser(Long userId, String email,
                                                         NotificationType type, NotificationChannel channel, Boolean enabled) {
        LocalDateTime now = LocalDateTime.now();

        // Check if preference already exists
        Optional<NotificationPreference> existing = findByUserTypeAndChannel(userId, type, channel);

        if (existing.isPresent()) {
            // Update existing preference
            return dsl.update(NOTIFICATION_PREFERENCE)
                    .set(NOTIFICATION_PREFERENCE.ENABLED, enabled)
                    .set(NOTIFICATION_PREFERENCE.EMAIL, email) // Update email in case it changed
                    .set(NOTIFICATION_PREFERENCE.UPDATED_AT, now)
                    .where(NOTIFICATION_PREFERENCE.ID.eq(existing.get().getId()))
                    .returning()
                    .fetchOneInto(NotificationPreference.class);
        } else {
            // Insert new preference
            return dsl.insertInto(NOTIFICATION_PREFERENCE)
                    .set(NOTIFICATION_PREFERENCE.USER_ID, userId)
                    .set(NOTIFICATION_PREFERENCE.EMAIL, email)
                    .set(NOTIFICATION_PREFERENCE.TYPE, type.toJooqEnum())
                    .set(NOTIFICATION_PREFERENCE.CHANNEL, channel.toJooqEnum())
                    .set(NOTIFICATION_PREFERENCE.ENABLED, enabled)
                    .set(NOTIFICATION_PREFERENCE.CREATED_AT, now)
                    .returning()
                    .fetchOneInto(NotificationPreference.class);
        }
    }

    /**
     * Creates or updates a notification preference for an external participant.
     * External participants have global preferences based only on email.
     *
     * @param email the participant email
     * @param type the notification type
     * @param channel the notification channel
     * @param enabled whether the preference is enabled
     * @return the created or updated preference
     */
    public NotificationPreference upsertPreferenceForEmail(String email, NotificationType type, NotificationChannel channel, Boolean enabled) {
        LocalDateTime now = LocalDateTime.now();

        // Check if preference already exists for this email
        Optional<NotificationPreference> existing = findByEmailTypeAndChannel(email, type, channel);

        if (existing.isPresent()) {
            // Update existing preference
            return dsl.update(NOTIFICATION_PREFERENCE)
                    .set(NOTIFICATION_PREFERENCE.ENABLED, enabled)
                    .set(NOTIFICATION_PREFERENCE.UPDATED_AT, now)
                    .where(NOTIFICATION_PREFERENCE.ID.eq(existing.get().getId()))
                    .returning()
                    .fetchOneInto(NotificationPreference.class);
        } else {
            // Insert new preference
            return dsl.insertInto(NOTIFICATION_PREFERENCE)
                    .set(NOTIFICATION_PREFERENCE.USER_ID, (Long) null)
                    .set(NOTIFICATION_PREFERENCE.EMAIL, email)
                    .set(NOTIFICATION_PREFERENCE.TYPE, type.toJooqEnum())
                    .set(NOTIFICATION_PREFERENCE.CHANNEL, channel.toJooqEnum())
                    .set(NOTIFICATION_PREFERENCE.ENABLED, enabled)
                    .set(NOTIFICATION_PREFERENCE.CREATED_AT, now)
                    .returning()
                    .fetchOneInto(NotificationPreference.class);
        }
    }

    /**
     * Legacy method for backward compatibility.
     * @deprecated Use upsertPreferenceForUser instead
     */
    @Deprecated
    public NotificationPreference upsertPreference(Long userId, NotificationType type, NotificationChannel channel, Boolean enabled) {
        // This method is kept for backward compatibility during migration
        // It will be removed once all callers are updated
        throw new UnsupportedOperationException("Use upsertPreferenceForUser or upsertPreferenceForEmail instead");
    }

    /**
     * Checks if a user has a specific notification type enabled for a channel.
     *
     * @param userId the user ID
     * @param type the notification type
     * @param channel the notification channel
     * @return true if enabled, false if disabled, null if no preference exists (defaults to enabled)
     */
    public Boolean isNotificationEnabled(Long userId, NotificationType type, NotificationChannel channel) {
        return dsl.select(NOTIFICATION_PREFERENCE.ENABLED)
                .from(NOTIFICATION_PREFERENCE)
                .where(NOTIFICATION_PREFERENCE.USER_ID.eq(userId))
                .and(NOTIFICATION_PREFERENCE.TYPE.eq(type.toJooqEnum()))
                .and(NOTIFICATION_PREFERENCE.CHANNEL.eq(channel.toJooqEnum()))
                .fetchOneInto(Boolean.class);
    }

    /**
     * Checks if an email has a specific notification type enabled for a channel.
     * Used for external participants with global preferences.
     *
     * @param email the email address
     * @param type the notification type
     * @param channel the notification channel
     * @return true if enabled, false if disabled, null if no preference exists (defaults to enabled)
     */
    public Boolean isNotificationEnabledForEmail(String email, NotificationType type, NotificationChannel channel) {
        return dsl.select(NOTIFICATION_PREFERENCE.ENABLED)
                .from(NOTIFICATION_PREFERENCE)
                .where(NOTIFICATION_PREFERENCE.EMAIL.eq(email))
                .and(NOTIFICATION_PREFERENCE.USER_ID.isNull())
                .and(NOTIFICATION_PREFERENCE.TYPE.eq(type.toJooqEnum()))
                .and(NOTIFICATION_PREFERENCE.CHANNEL.eq(channel.toJooqEnum()))
                .fetchOneInto(Boolean.class);
    }
}
