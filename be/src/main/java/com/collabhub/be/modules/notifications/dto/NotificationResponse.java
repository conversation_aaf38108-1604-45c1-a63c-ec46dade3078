package com.collabhub.be.modules.notifications.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * Response DTO for notification data.
 * Contains all notification information for display in the UI.
 */
@Schema(description = "Notification response containing notification details")
public class NotificationResponse {

    @Schema(description = "Unique notification identifier", example = "1")
    private Long id;

    @Schema(description = "User ID who should receive this notification", example = "123")
    private Long userId;

    @Schema(description = "Type of notification", enumAsRef = true)
    private NotificationType type;

    @Schema(description = "Short notification title", example = "New comment on your post")
    private String title;

    @Schema(description = "Full notification message", example = "<PERSON> commented on your post 'Summer Campaign'")
    private String message;

    @Schema(description = "Read status of the notification", enumAsRef = true)
    private NotificationStatus status;

    @Schema(description = "Related collaboration hub ID", example = "456")
    private Long collaborationHubId;

    @Schema(description = "Related post ID", example = "789")
    private Long postId;

    @Schema(description = "Related comment ID", example = "101")
    private Long commentId;

    @Schema(description = "Related chat channel ID", example = "202")
    private Long chatChannelId;

    @Schema(description = "When the notification was created")
    private LocalDateTime createdAt;

    @Schema(description = "When the notification was read (null if unread)")
    private LocalDateTime readAt;

    // Constructors
    public NotificationResponse() {}

    public NotificationResponse(Long id, Long userId, NotificationType type, String title, String message,
                              NotificationStatus status, Long collaborationHubId, Long postId, Long commentId,
                              Long chatChannelId, LocalDateTime createdAt, LocalDateTime readAt) {
        this.id = id;
        this.userId = userId;
        this.type = type;
        this.title = title;
        this.message = message;
        this.status = status;
        this.collaborationHubId = collaborationHubId;
        this.postId = postId;
        this.commentId = commentId;
        this.chatChannelId = chatChannelId;
        this.createdAt = createdAt;
        this.readAt = readAt;
    }

    // Getters and setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    public NotificationType getType() { return type; }
    public void setType(NotificationType type) { this.type = type; }

    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }

    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }

    public NotificationStatus getStatus() { return status; }
    public void setStatus(NotificationStatus status) { this.status = status; }

    public Long getCollaborationHubId() { return collaborationHubId; }
    public void setCollaborationHubId(Long collaborationHubId) { this.collaborationHubId = collaborationHubId; }

    public Long getPostId() { return postId; }
    public void setPostId(Long postId) { this.postId = postId; }

    public Long getCommentId() { return commentId; }
    public void setCommentId(Long commentId) { this.commentId = commentId; }

    public Long getChatChannelId() { return chatChannelId; }
    public void setChatChannelId(Long chatChannelId) { this.chatChannelId = chatChannelId; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getReadAt() { return readAt; }
    public void setReadAt(LocalDateTime readAt) { this.readAt = readAt; }
}
