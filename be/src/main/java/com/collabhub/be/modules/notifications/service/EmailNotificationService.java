package com.collabhub.be.modules.notifications.service;

import com.collabhub.be.modules.notifications.constants.NotificationConstants;
import com.collabhub.be.modules.notifications.dto.NotificationType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Service for handling email notification delivery with flood control.
 * Implements batching and rate limiting to prevent email spam.
 */
@Service
public class EmailNotificationService {

    private static final Logger logger = LoggerFactory.getLogger(EmailNotificationService.class);

    // Flood control constants
    private static final int MAX_EMAILS_PER_USER_PER_HOUR = 10;
    private static final int BATCH_SIZE = 50;
    private static final int BATCH_DELAY_MINUTES = 5;

    // In-memory flood control tracking (in production, consider Redis)
    private final Map<Long, List<LocalDateTime>> userEmailHistory = new ConcurrentHashMap<>();

    /**
     * Sends email notifications to multiple users with flood control.
     *
     * @param type the notification type
     * @param title the email subject
     * @param message the email content
     * @param recipientUserIds the user IDs to send emails to
     * @param entityReferences optional entity references for email context
     */
    @Async
    @Transactional
    public void sendEmailNotifications(NotificationType type, String title, String message,
                                     Set<Long> recipientUserIds, 
                                     NotificationStorageService.EntityReferences entityReferences) {
        
        logger.debug("Processing email notifications: type={}, recipients={}", type, recipientUserIds.size());

        // Apply flood control filtering
        Set<Long> filteredRecipients = applyFloodControl(recipientUserIds);
        
        if (filteredRecipients.isEmpty()) {
            logger.info("No recipients after flood control filtering for notification type: {}", type);
            return;
        }

        // Batch the recipients to avoid overwhelming the email service
        List<Set<Long>> batches = createBatches(filteredRecipients, BATCH_SIZE);
        
        logger.info("Sending {} email notifications in {} batches for type: {}", 
                   filteredRecipients.size(), batches.size(), type);

        for (int i = 0; i < batches.size(); i++) {
            Set<Long> batch = batches.get(i);
            
            // Add delay between batches (except for the first one)
            if (i > 0) {
                try {
                    Thread.sleep(BATCH_DELAY_MINUTES * 60 * 1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    logger.warn("Email batch processing interrupted", e);
                    return;
                }
            }
            
            sendEmailBatch(type, title, message, batch, entityReferences);
        }

        logger.info("Completed email notification delivery for type: {}", type);
    }

    /**
     * Applies flood control to filter out users who have received too many emails recently.
     */
    private Set<Long> applyFloodControl(Set<Long> recipientUserIds) {
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        
        return recipientUserIds.stream()
                .filter(userId -> {
                    List<LocalDateTime> userHistory = userEmailHistory.getOrDefault(userId, new ArrayList<>());
                    
                    // Remove old entries
                    userHistory.removeIf(timestamp -> timestamp.isBefore(oneHourAgo));
                    
                    // Check if user is under the limit
                    boolean underLimit = userHistory.size() < MAX_EMAILS_PER_USER_PER_HOUR;
                    
                    if (underLimit) {
                        // Add current timestamp
                        userHistory.add(LocalDateTime.now());
                        userEmailHistory.put(userId, userHistory);
                    } else {
                        logger.debug("User {} excluded from email notification due to flood control", userId);
                    }
                    
                    return underLimit;
                })
                .collect(Collectors.toSet());
    }

    /**
     * Creates batches of user IDs for processing.
     */
    private List<Set<Long>> createBatches(Set<Long> userIds, int batchSize) {
        List<Set<Long>> batches = new ArrayList<>();
        Set<Long> currentBatch = new HashSet<>();
        
        for (Long userId : userIds) {
            currentBatch.add(userId);
            
            if (currentBatch.size() >= batchSize) {
                batches.add(new HashSet<>(currentBatch));
                currentBatch.clear();
            }
        }
        
        // Add remaining items as the last batch
        if (!currentBatch.isEmpty()) {
            batches.add(currentBatch);
        }
        
        return batches;
    }

    /**
     * Sends a batch of emails.
     * This is a placeholder - actual implementation would integrate with email service.
     */
    private void sendEmailBatch(NotificationType type, String title, String message,
                              Set<Long> recipientUserIds, 
                              NotificationStorageService.EntityReferences entityReferences) {
        
        logger.debug("Sending email batch: type={}, recipients={}", type, recipientUserIds.size());

        // TODO: Implement actual email sending logic
        // This would typically:
        // 1. Fetch user email addresses from user IDs
        // 2. Generate email content using templates
        // 3. Send emails via email service (SendGrid, SES, etc.)
        // 4. Handle delivery failures and retries
        
        for (Long userId : recipientUserIds) {
            logger.info("Would send email notification: user={}, type={}, title={}", userId, type, title);
        }

        logger.debug("Completed email batch: type={}, recipients={}", type, recipientUserIds.size());
    }

    /**
     * Cleans up old email history entries to prevent memory leaks.
     * Should be called periodically.
     */
    public void cleanupEmailHistory() {
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        
        userEmailHistory.entrySet().removeIf(entry -> {
            List<LocalDateTime> history = entry.getValue();
            history.removeIf(timestamp -> timestamp.isBefore(oneHourAgo));
            return history.isEmpty();
        });
        
        logger.debug("Cleaned up email history, {} users remaining", userEmailHistory.size());
    }
}
