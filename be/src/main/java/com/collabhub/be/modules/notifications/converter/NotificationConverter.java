package com.collabhub.be.modules.notifications.converter;

import com.collabhub.be.modules.notifications.dto.NotificationResponse;
import com.collabhub.be.modules.notifications.dto.NotificationStatus;
import com.collabhub.be.modules.notifications.dto.NotificationType;
import org.jooq.generated.tables.pojos.Notification;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Converter for Notification entities and DTOs.
 * Handles conversion between jOOQ POJOs and API response DTOs.
 */
@Component
public class NotificationConverter {

    /**
     * Converts a Notification entity to a response DTO.
     *
     * @param notification the notification entity
     * @return the response DTO
     */
    public NotificationResponse toResponse(Notification notification) {
        if (notification == null) {
            return null;
        }

        return new NotificationResponse(
                notification.getId(),
                notification.getUserId(),
                NotificationType.fromJooqEnum(notification.getType()),
                notification.getTitle(),
                notification.getMessage(),
                NotificationStatus.fromJooqEnum(notification.getStatus()),
                notification.getCollaborationHubId(),
                notification.getPostId(),
                notification.getCommentId(),
                notification.getChatChannelId(),
                notification.getCreatedAt(),
                notification.getReadAt()
        );
    }

    /**
     * Converts a list of Notification entities to response DTOs.
     *
     * @param notifications the list of notification entities
     * @return the list of response DTOs
     */
    public List<NotificationResponse> toResponseList(List<Notification> notifications) {
        if (notifications == null) {
            return List.of();
        }

        return notifications.stream()
                .map(this::toResponse)
                .collect(Collectors.toList());
    }
}
