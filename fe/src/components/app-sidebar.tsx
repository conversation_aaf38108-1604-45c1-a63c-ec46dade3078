import * as React from "react"
import { useLocation, useNavigate } from 'react-router';

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from '@/components/ui/sidebar';
import { GalleryVerticalEnd, LayoutDashboard, Building2, CreditCard, Tag, FileText, Users, Settings } from 'lucide-react'
import { ROUTES } from "@/router/routes"
import { usePermissions } from '@/hooks/use-permissions'
import { Permission } from '@/lib/types/api'
import { NavUser } from '@/components/nav-user'
import { useCurrentUser } from '@/contexts/auth-context'

// Navigation items with permission requirements
const navItems = [
  {
    title: "Dashboard",
    url: ROUTES.DASHBOARD,
    icon: LayoutDashboard,
    // Dashboard is always visible for authenticated users
    requiredPermission: null
  },
  {
    title: "Account companies",
    url: ROUTES.ACCOUNT_COMPANIES,
    icon: Building2,
    requiredPermission: Permission.COMPANY_READ
  },
  {
    title: "Bank Details",
    url: ROUTES.BANK_DETAILS,
    icon: CreditCard,
    requiredPermission: Permission.BANK_READ
  },
  {
    title: "Brands",
    url: ROUTES.BRANDS,
    icon: Tag,
    requiredPermission: Permission.BRAND_READ
  },
  {
    title: "Hubs",
    url: ROUTES.COLLABORATION_HUBS,
    icon: Users,
    requiredPermission: Permission.HUB_READ
  },
  {
    title: "Invoices",
    url: ROUTES.INVOICES,
    icon: FileText,
    requiredPermission: Permission.INVOICE_READ
  },
  {
    title: "Settings",
    url: ROUTES.SETTINGS,
    icon: Settings,
    // Settings is always visible for authenticated users
    requiredPermission: null
  },
]

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const location = useLocation()
  const navigate = useNavigate()
  const { hasPermission } = usePermissions()
  const user = useCurrentUser()

  // Filter navigation items based on user permissions
  const visibleNavItems = navItems.filter(item => {
    // Always show items that don't require specific permissions (like Dashboard)
    if (item.requiredPermission === null) return true

    // Show items only if user has the required permission
    return hasPermission(item.requiredPermission)
  })

  return (
    <Sidebar {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                <GalleryVerticalEnd className="size-4" />
              </div>
              <div className="flex flex-col gap-0.5 leading-none">
                <span className="font-medium">Collaboration Hub</span>
              </div>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent className="flex flex-col gap-2">
            <SidebarMenu>
              {visibleNavItems.map((item) => {
                const isActive = location.pathname === item.url
                return (
                  <SidebarMenuItem key={item.title} onClick={() => navigate(item.url)}>
                    <SidebarMenuButton isActive={isActive}>
                      {item.icon && <item.icon />}
                      {item.title}
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                )
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      {user && (
        <SidebarFooter className="p-2">
          <NavUser />
        </SidebarFooter>
      )}
      <SidebarRail />
    </Sidebar>
  )
}
